{"clientVersion":"5.22.0","errorCode":"P1001","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mDatabase connection failed: Can't reach database server at `localhost:5433`\u001b[39m\n\n\u001b[31mPlease make sure your database server is running at `localhost:5433`.\u001b[39m","name":"PrismaClientInitializationError","stack":"PrismaClientInitializationError: Can't reach database server at `localhost:5433`\n\nPlease make sure your database server is running at `localhost:5433`.\n    at t (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:112:2488)\n    at async connectDatabase (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\config\\database.ts:28:5)\n    at async startServer (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\server.ts:45:5)","timestamp":"2025-06-08 21:05:05:55"}
{"clientVersion":"5.22.0","errorCode":"P1000","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mDatabase connection failed: Authentication failed against database server at `localhost`, the provided database credentials for `postgres` are not valid.\u001b[39m\n\n\u001b[31mPlease make sure to provide valid database credentials for the database server at `localhost`.\u001b[39m","name":"PrismaClientInitializationError","stack":"PrismaClientInitializationError: Authentication failed against database server at `localhost`, the provided database credentials for `postgres` are not valid.\n\nPlease make sure to provide valid database credentials for the database server at `localhost`.\n    at t (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:112:2488)\n    at async connectDatabase (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\config\\database.ts:28:5)\n    at async startServer (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\server.ts:45:5)","timestamp":"2025-06-08 21:07:47:747"}
{"code":"EAUTH","command":"AUTH PLAIN","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mEmail service connection failed: Invalid login: 535-5.7.8 Username and Password not accepted. For more information, go to\u001b[39m\n\u001b[31m535 5.7.8  https://support.google.com/mail/?p=BadCredentials 98e67ed59e1d1-31349ffc151sm4149856a91.48 - gsmtp\u001b[39m","response":"535-5.7.8 Username and Password not accepted. For more information, go to\n535 5.7.8  https://support.google.com/mail/?p=BadCredentials 98e67ed59e1d1-31349ffc151sm4149856a91.48 - gsmtp","responseCode":535,"stack":"Error: Invalid login: 535-5.7.8 Username and Password not accepted. For more information, go to\n535 5.7.8  https://support.google.com/mail/?p=BadCredentials 98e67ed59e1d1-31349ffc151sm4149856a91.48 - gsmtp\n    at SMTPConnection._formatError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:809:19)\n    at SMTPConnection._actionAUTHComplete (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:1588:34)\n    at SMTPConnection.<anonymous> (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:556:26)\n    at SMTPConnection._processResponse (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:993:20)\n    at SMTPConnection._onData (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:774:14)\n    at TLSSocket.SMTPConnection._onSocketData (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:195:44)\n    at TLSSocket.emit (node:events:524:28)\n    at TLSSocket.emit (node:domain:489:12)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)","timestamp":"2025-06-08 21:08:20:820"}
{"clientVersion":"5.22.0","code":"P2021","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError sending OTP: \u001b[39m\n\u001b[31mInvalid `prisma.user.findUnique()` invocation in\u001b[39m\n\u001b[31mC:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:44:36\u001b[39m\n\n\u001b[31m  41 const username = extractNameFromEmail(email);\u001b[39m\n\u001b[31m  42 \u001b[39m\n\u001b[31m  43 // Find or create user\u001b[39m\n\u001b[31m→ 44 let user = await prisma.user.findUnique(\u001b[39m\n\u001b[31mThe table `public.users` does not exist in the current database.\u001b[39m","meta":{"modelName":"User","table":"public.users"},"name":"PrismaClientKnownRequestError","stack":"PrismaClientKnownRequestError: \nInvalid `prisma.user.findUnique()` invocation in\nC:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:44:36\n\n  41 const username = extractNameFromEmail(email);\n  42 \n  43 // Find or create user\n→ 44 let user = await prisma.user.findUnique(\nThe table `public.users` does not exist in the current database.\n    at $n.handleRequestError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:7315)\n    at $n.handleAndLogRequestError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6623)\n    at $n.request (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6307)\n    at async l (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:130:9633)\n    at async AuthService.sendOTP (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:44:18)\n    at async sendOTP (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\controllers\\auth.controller.ts:14:22)","timestamp":"2025-06-08 21:23:41:2341"}
{"ip":"::ffff:***************","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: \u001b[39m\n\u001b[31mInvalid `prisma.user.findUnique()` invocation in\u001b[39m\n\u001b[31mC:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:44:36\u001b[39m\n\n\u001b[31m  41 const username = extractNameFromEmail(email);\u001b[39m\n\u001b[31m  42 \u001b[39m\n\u001b[31m  43 // Find or create user\u001b[39m\n\u001b[31m→ 44 let user = await prisma.user.findUnique(\u001b[39m\n\u001b[31mThe table `public.users` does not exist in the current database.\u001b[39m","method":"POST","stack":"PrismaClientKnownRequestError: \nInvalid `prisma.user.findUnique()` invocation in\nC:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:44:36\n\n  41 const username = extractNameFromEmail(email);\n  42 \n  43 // Find or create user\n→ 44 let user = await prisma.user.findUnique(\nThe table `public.users` does not exist in the current database.\n    at $n.handleRequestError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:7315)\n    at $n.handleAndLogRequestError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6623)\n    at $n.request (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6307)\n    at async l (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:130:9633)\n    at async AuthService.sendOTP (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:44:18)\n    at async sendOTP (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\controllers\\auth.controller.ts:14:22)","timestamp":"2025-06-08 21:23:41:2341","url":"/api/auth/send-otp","userAgent":"okhttp/4.12.0"}
{"clientVersion":"5.22.0","code":"P2021","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError sending OTP: \u001b[39m\n\u001b[31mInvalid `prisma.user.findUnique()` invocation in\u001b[39m\n\u001b[31mC:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:44:36\u001b[39m\n\n\u001b[31m  41 const username = extractNameFromEmail(email);\u001b[39m\n\u001b[31m  42 \u001b[39m\n\u001b[31m  43 // Find or create user\u001b[39m\n\u001b[31m→ 44 let user = await prisma.user.findUnique(\u001b[39m\n\u001b[31mThe table `public.users` does not exist in the current database.\u001b[39m","meta":{"modelName":"User","table":"public.users"},"name":"PrismaClientKnownRequestError","stack":"PrismaClientKnownRequestError: \nInvalid `prisma.user.findUnique()` invocation in\nC:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:44:36\n\n  41 const username = extractNameFromEmail(email);\n  42 \n  43 // Find or create user\n→ 44 let user = await prisma.user.findUnique(\nThe table `public.users` does not exist in the current database.\n    at $n.handleRequestError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:7315)\n    at $n.handleAndLogRequestError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6623)\n    at $n.request (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6307)\n    at async l (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:130:9633)\n    at async AuthService.sendOTP (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:44:18)\n    at async sendOTP (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\controllers\\auth.controller.ts:14:22)","timestamp":"2025-06-08 21:27:05:275"}
{"ip":"***************","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: \u001b[39m\n\u001b[31mInvalid `prisma.user.findUnique()` invocation in\u001b[39m\n\u001b[31mC:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:44:36\u001b[39m\n\n\u001b[31m  41 const username = extractNameFromEmail(email);\u001b[39m\n\u001b[31m  42 \u001b[39m\n\u001b[31m  43 // Find or create user\u001b[39m\n\u001b[31m→ 44 let user = await prisma.user.findUnique(\u001b[39m\n\u001b[31mThe table `public.users` does not exist in the current database.\u001b[39m","method":"POST","stack":"PrismaClientKnownRequestError: \nInvalid `prisma.user.findUnique()` invocation in\nC:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:44:36\n\n  41 const username = extractNameFromEmail(email);\n  42 \n  43 // Find or create user\n→ 44 let user = await prisma.user.findUnique(\nThe table `public.users` does not exist in the current database.\n    at $n.handleRequestError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:7315)\n    at $n.handleAndLogRequestError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6623)\n    at $n.request (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6307)\n    at async l (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:130:9633)\n    at async AuthService.sendOTP (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:44:18)\n    at async sendOTP (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\controllers\\auth.controller.ts:14:22)","timestamp":"2025-06-08 21:27:05:275","url":"/api/auth/send-otp","userAgent":"okhttp/4.12.0"}
{"clientVersion":"5.22.0","code":"P2021","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError sending OTP: \u001b[39m\n\u001b[31mInvalid `prisma.user.findUnique()` invocation in\u001b[39m\n\u001b[31mC:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:44:36\u001b[39m\n\n\u001b[31m  41 const username = extractNameFromEmail(email);\u001b[39m\n\u001b[31m  42 \u001b[39m\n\u001b[31m  43 // Find or create user\u001b[39m\n\u001b[31m→ 44 let user = await prisma.user.findUnique(\u001b[39m\n\u001b[31mThe table `public.users` does not exist in the current database.\u001b[39m","meta":{"modelName":"User","table":"public.users"},"name":"PrismaClientKnownRequestError","stack":"PrismaClientKnownRequestError: \nInvalid `prisma.user.findUnique()` invocation in\nC:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:44:36\n\n  41 const username = extractNameFromEmail(email);\n  42 \n  43 // Find or create user\n→ 44 let user = await prisma.user.findUnique(\nThe table `public.users` does not exist in the current database.\n    at $n.handleRequestError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:7315)\n    at $n.handleAndLogRequestError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6623)\n    at $n.request (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6307)\n    at async l (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:130:9633)\n    at async AuthService.sendOTP (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:44:18)\n    at async sendOTP (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\controllers\\auth.controller.ts:14:22)","timestamp":"2025-06-08 21:27:14:2714"}
{"ip":"***************","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: \u001b[39m\n\u001b[31mInvalid `prisma.user.findUnique()` invocation in\u001b[39m\n\u001b[31mC:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:44:36\u001b[39m\n\n\u001b[31m  41 const username = extractNameFromEmail(email);\u001b[39m\n\u001b[31m  42 \u001b[39m\n\u001b[31m  43 // Find or create user\u001b[39m\n\u001b[31m→ 44 let user = await prisma.user.findUnique(\u001b[39m\n\u001b[31mThe table `public.users` does not exist in the current database.\u001b[39m","method":"POST","stack":"PrismaClientKnownRequestError: \nInvalid `prisma.user.findUnique()` invocation in\nC:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:44:36\n\n  41 const username = extractNameFromEmail(email);\n  42 \n  43 // Find or create user\n→ 44 let user = await prisma.user.findUnique(\nThe table `public.users` does not exist in the current database.\n    at $n.handleRequestError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:7315)\n    at $n.handleAndLogRequestError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6623)\n    at $n.request (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6307)\n    at async l (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:130:9633)\n    at async AuthService.sendOTP (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:44:18)\n    at async sendOTP (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\controllers\\auth.controller.ts:14:22)","timestamp":"2025-06-08 21:27:14:2714","url":"/api/auth/send-otp","userAgent":"okhttp/4.12.0"}
{"clientVersion":"5.22.0","code":"P2021","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError sending OTP: \u001b[39m\n\u001b[31mInvalid `prisma.user.findUnique()` invocation in\u001b[39m\n\u001b[31mC:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:44:36\u001b[39m\n\n\u001b[31m  41 const username = extractNameFromEmail(email);\u001b[39m\n\u001b[31m  42 \u001b[39m\n\u001b[31m  43 // Find or create user\u001b[39m\n\u001b[31m→ 44 let user = await prisma.user.findUnique(\u001b[39m\n\u001b[31mThe table `public.users` does not exist in the current database.\u001b[39m","meta":{"modelName":"User","table":"public.users"},"name":"PrismaClientKnownRequestError","stack":"PrismaClientKnownRequestError: \nInvalid `prisma.user.findUnique()` invocation in\nC:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:44:36\n\n  41 const username = extractNameFromEmail(email);\n  42 \n  43 // Find or create user\n→ 44 let user = await prisma.user.findUnique(\nThe table `public.users` does not exist in the current database.\n    at $n.handleRequestError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:7315)\n    at $n.handleAndLogRequestError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6623)\n    at $n.request (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6307)\n    at async l (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:130:9633)\n    at async AuthService.sendOTP (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:44:18)\n    at async sendOTP (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\controllers\\auth.controller.ts:14:22)","timestamp":"2025-06-08 21:27:57:2757"}
{"ip":"***************","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: \u001b[39m\n\u001b[31mInvalid `prisma.user.findUnique()` invocation in\u001b[39m\n\u001b[31mC:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:44:36\u001b[39m\n\n\u001b[31m  41 const username = extractNameFromEmail(email);\u001b[39m\n\u001b[31m  42 \u001b[39m\n\u001b[31m  43 // Find or create user\u001b[39m\n\u001b[31m→ 44 let user = await prisma.user.findUnique(\u001b[39m\n\u001b[31mThe table `public.users` does not exist in the current database.\u001b[39m","method":"POST","stack":"PrismaClientKnownRequestError: \nInvalid `prisma.user.findUnique()` invocation in\nC:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:44:36\n\n  41 const username = extractNameFromEmail(email);\n  42 \n  43 // Find or create user\n→ 44 let user = await prisma.user.findUnique(\nThe table `public.users` does not exist in the current database.\n    at $n.handleRequestError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:7315)\n    at $n.handleAndLogRequestError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6623)\n    at $n.request (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6307)\n    at async l (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:130:9633)\n    at async AuthService.sendOTP (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:44:18)\n    at async sendOTP (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\controllers\\auth.controller.ts:14:22)","timestamp":"2025-06-08 21:27:57:2757","url":"/api/auth/send-otp","userAgent":"okhttp/4.12.0"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mPort 3000 is already in use\u001b[39m","timestamp":"2025-06-08 21:30:19:3019"}
{"code":"AUTHENTICATION_ERROR","isOperational":true,"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError verifying OTP: Invalid or expired OTP\u001b[39m","name":"AuthenticationError","stack":"AuthenticationError: Invalid or expired OTP\n    at AuthService.verifyOTP (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:131:15)\n    at async verifyOTP (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\controllers\\auth.controller.ts:29:22)","statusCode":401,"timestamp":"2025-06-08 21:31:23:3123"}
{"ip":"***************","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Invalid or expired OTP\u001b[39m","method":"POST","stack":"AuthenticationError: Invalid or expired OTP\n    at AuthService.verifyOTP (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:131:15)\n    at async verifyOTP (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\controllers\\auth.controller.ts:29:22)","timestamp":"2025-06-08 21:31:23:3123","url":"/api/auth/verify-otp","userAgent":"okhttp/4.12.0"}
{"code":"CONFLICT_ERROR","isOperational":true,"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError deleting project: Cannot delete project with existing financial records\u001b[39m","name":"ConflictError","stack":"ConflictError: Cannot delete project with existing financial records\n    at ProjectService.deleteProject (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\services\\project.service.ts:645:15)\n    at async deleteProject (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\controllers\\project.controller.ts:238:22)","statusCode":409,"timestamp":"2025-06-08 21:32:22:3222"}
{"ip":"***************","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Cannot delete project with existing financial records\u001b[39m","method":"DELETE","stack":"ConflictError: Cannot delete project with existing financial records\n    at ProjectService.deleteProject (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\services\\project.service.ts:645:15)\n    at async deleteProject (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\controllers\\project.controller.ts:238:22)","timestamp":"2025-06-08 21:32:22:3222","url":"/api/projects/2","userAgent":"okhttp/4.12.0"}
{"code":"CONFLICT_ERROR","isOperational":true,"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError deleting client: Cannot delete client with existing projects\u001b[39m","name":"ConflictError","stack":"ConflictError: Cannot delete client with existing projects\n    at ClientService.deleteClient (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\services\\client.service.ts:338:15)\n    at async deleteClient (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\controllers\\client.controller.ts:167:22)","statusCode":409,"timestamp":"2025-06-09 00:02:23:223"}
{"ip":"***************","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Cannot delete client with existing projects\u001b[39m","method":"DELETE","stack":"ConflictError: Cannot delete client with existing projects\n    at ClientService.deleteClient (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\services\\client.service.ts:338:15)\n    at async deleteClient (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\controllers\\client.controller.ts:167:22)","timestamp":"2025-06-09 00:02:23:223","url":"/api/clients/2","userAgent":"okhttp/4.12.0"}
{"code":"CONFLICT_ERROR","isOperational":true,"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError deleting client: Cannot delete client with existing projects\u001b[39m","name":"ConflictError","stack":"ConflictError: Cannot delete client with existing projects\n    at ClientService.deleteClient (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\services\\client.service.ts:338:15)\n    at async deleteClient (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\controllers\\client.controller.ts:167:22)","statusCode":409,"timestamp":"2025-06-09 00:02:26:226"}
{"ip":"***************","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Cannot delete client with existing projects\u001b[39m","method":"DELETE","stack":"ConflictError: Cannot delete client with existing projects\n    at ClientService.deleteClient (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\services\\client.service.ts:338:15)\n    at async deleteClient (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\controllers\\client.controller.ts:167:22)","timestamp":"2025-06-09 00:02:26:226","url":"/api/clients/2","userAgent":"okhttp/4.12.0"}
{"code":"CONFLICT_ERROR","isOperational":true,"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError deleting client: Cannot delete client with existing projects\u001b[39m","name":"ConflictError","stack":"ConflictError: Cannot delete client with existing projects\n    at ClientService.deleteClient (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\services\\client.service.ts:338:15)\n    at async deleteClient (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\controllers\\client.controller.ts:167:22)","statusCode":409,"timestamp":"2025-06-09 01:07:58:758"}
{"ip":"***************","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Cannot delete client with existing projects\u001b[39m","method":"DELETE","stack":"ConflictError: Cannot delete client with existing projects\n    at ClientService.deleteClient (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\services\\client.service.ts:338:15)\n    at async deleteClient (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\controllers\\client.controller.ts:167:22)","timestamp":"2025-06-09 01:07:58:758","url":"/api/clients/2","userAgent":"okhttp/4.12.0"}
{"code":"CONFLICT_ERROR","isOperational":true,"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError deleting client: Cannot delete client with existing projects\u001b[39m","name":"ConflictError","stack":"ConflictError: Cannot delete client with existing projects\n    at ClientService.deleteClient (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\services\\client.service.ts:338:15)\n    at async deleteClient (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\controllers\\client.controller.ts:167:22)","statusCode":409,"timestamp":"2025-06-09 01:08:00:80"}
{"ip":"***************","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Cannot delete client with existing projects\u001b[39m","method":"DELETE","stack":"ConflictError: Cannot delete client with existing projects\n    at ClientService.deleteClient (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\services\\client.service.ts:338:15)\n    at async deleteClient (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\controllers\\client.controller.ts:167:22)","timestamp":"2025-06-09 01:08:00:80","url":"/api/clients/2","userAgent":"okhttp/4.12.0"}
